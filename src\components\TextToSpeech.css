.text-to-speech-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2.5rem;
}

.header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.close-error {
  background: none;
  border: none;
  color: #c33;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.input-section {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.voice-selector {
  margin-bottom: 20px;
}

.voice-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.voice-selector select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  background-color: white;
  margin-bottom: 10px;
}

.voice-selector select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.refresh-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #5a6268;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.text-input {
  margin-bottom: 20px;
}

.text-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.text-input textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
}

.text-input textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
}

.sample-texts {
  margin-bottom: 20px;
}

.sample-texts label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.sample-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.sample-btn {
  background-color: #e9ecef;
  color: #495057;
  border: 1px solid #ced4da;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.sample-btn:hover {
  background-color: #dee2e6;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.generate-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.generate-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.generate-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.audio-section {
  background: #e8f5e8;
  padding: 25px;
  border-radius: 12px;
  border: 1px solid #c3e6c3;
}

.audio-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #27ae60;
}

.audio-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.play-pause-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.play-pause-btn:hover {
  background: linear-gradient(135deg, #229954, #1e8449);
  transform: translateY(-1px);
}

.download-btn {
  background: linear-gradient(135deg, #8e44ad, #7d3c98);
  color: white;
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s;
  display: inline-block;
}

.download-btn:hover {
  background: linear-gradient(135deg, #7d3c98, #6c3483);
  transform: translateY(-1px);
}

.audio-debug {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  font-family: monospace;
  font-size: 12px;
  color: #6c757d;
}

.test-url-btn {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 5px;
}

.test-url-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #138496, #117a8b);
  transform: translateY(-1px);
}

.test-url-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.history-section {
  background: #fff3cd;
  padding: 25px;
  border-radius: 12px;
  border: 1px solid #ffeaa7;
}

.history-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #856404;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #f1c40f;
}

.speech-name {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
  margin-right: 10px;
}

.play-history-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.play-history-btn:hover {
  background: linear-gradient(135deg, #e67e22, #d35400);
  transform: translateY(-1px);
}

.loading {
  color: #6c757d;
  font-style: italic;
  padding: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
  .text-to-speech-container {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .input-section,
  .audio-section,
  .history-section {
    padding: 20px;
  }
  
  .audio-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .history-item {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .speech-name {
    margin-right: 0;
    margin-bottom: 5px;
  }
}
