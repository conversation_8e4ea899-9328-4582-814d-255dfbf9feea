# 🎤 Tavus Text-to-Speech Demo

A React-based demo application showcasing the Tavus API for AI-powered text-to-speech generation with realistic voice replicas.

## Features

- 🎵 **AI Voice Generation**: Convert text to natural-sounding speech using Tavus AI replicas
- 🎭 **Multiple Voice Options**: Choose from available user and system voice replicas
- 🎧 **Audio Playback**: Play generated speech directly in the browser
- 💾 **Download Support**: Download generated audio files
- 📝 **Sample Texts**: Quick-load sample texts for testing
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🔄 **Speech History**: Keep track of recently generated speeches

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Tavus API key

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd text-to-speech
```

2. Install dependencies:
```bash
npm install
```

3. Update the API key in `src/services/tavusApi.ts`:
```typescript
const API_KEY = 'your-tavus-api-key-here';
```

4. Start the development server:
```bash
npm run dev
```

5. Open your browser and navigate to `http://localhost:5173`

## Usage

1. **Select a Voice**: Choose from available voice replicas in the dropdown
2. **Enter Text**: Type or paste the text you want to convert to speech (up to 1000 characters)
3. **Use Samples**: Click on sample buttons to quickly load example texts
4. **Generate Speech**: Click the "Generate Speech" button to create audio
5. **Play Audio**: Use the play/pause controls to listen to the generated speech
6. **Download**: Save the audio file to your device using the download button
7. **History**: Access recently generated speeches from the history section

## API Integration

This demo uses the Tavus API v2 for speech generation. The main endpoints used are:

- `GET /v2/replicas` - Fetch available voice replicas
- `POST /v2/speech` - Generate speech from text
- `GET /v2/speech/{id}` - Get speech details

### API Configuration

The API service is configured in `src/services/tavusApi.ts` with:
- Base URL: `https://tavusapi.com/v2`
- Authentication: API key in `x-api-key` header
- Callback URL: Uses `https://httpbin.org/post` for demo purposes

## Project Structure

```
src/
├── components/
│   ├── TextToSpeech.tsx      # Main component
│   └── TextToSpeech.css      # Component styles
├── services/
│   └── tavusApi.ts           # Tavus API integration
├── App.tsx                   # Root component
├── App.css                   # App styles
├── index.css                 # Global styles
└── main.tsx                  # Entry point
```

## Technologies Used

- **React 19** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tavus API** - AI voice generation
- **CSS3** - Styling with gradients and animations

## Error Handling

The application includes comprehensive error handling for:
- Authentication failures (401)
- Permission issues (403)
- Rate limiting (429)
- Invalid requests (400)
- Network connectivity issues

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for demonstration purposes. Please check Tavus API terms of service for commercial usage.

## Support

For issues related to:
- **Tavus API**: Contact [Tavus Support](mailto:<EMAIL>)
- **Demo Application**: Create an issue in this repository

---

Built with ❤️ using Tavus AI technology
