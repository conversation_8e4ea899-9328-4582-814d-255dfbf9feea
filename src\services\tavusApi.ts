// Tavus API service for text-to-speech functionality
const TAVUS_API_BASE_URL = 'https://tavusapi.com/v2';
const API_KEY = 'c638c5aafa58438caa874d63cb00ae1b';

export interface TavusReplica {
  replica_id: string;
  replica_name: string;
  thumbnail_video_url: string;
  training_progress: string;
  status: string;
  created_at: string;
  replica_type: 'user' | 'system';
}

export interface TavusReplicasResponse {
  data: TavusReplica[];
  total_count: number;
}

export interface TavusSpeechRequest {
  script: string;
  replica_id: string;
  speech_name?: string;
  callback_url?: string;
}

export interface TavusSpeechResponse {
  speech_id: string;
  speech_name: string;
  speech_file_url: string;
}

export interface TavusSpeechStatus {
  speech_id: string;
  speech_name: string;
  speech_file_url: string;
  status: string;
  created_at: string;
}

class TavusApiService {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string = API_KEY, baseUrl: string = TAVUS_API_BASE_URL) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.apiKey,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Tavus API Error (${response.status}): ${errorText}`);
    }

    return response.json();
  }

  // Get list of available replicas
  async getReplicas(params?: {
    limit?: number;
    page?: number;
    verbose?: boolean;
    replica_type?: 'user' | 'system';
    replica_ids?: string;
  }): Promise<TavusReplicasResponse> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.verbose) searchParams.append('verbose', params.verbose.toString());
    if (params?.replica_type) searchParams.append('replica_type', params.replica_type);
    if (params?.replica_ids) searchParams.append('replica_ids', params.replica_ids);

    const queryString = searchParams.toString();
    const endpoint = `/replicas${queryString ? `?${queryString}` : ''}`;

    return this.makeRequest<TavusReplicasResponse>(endpoint);
  }

  // Generate speech from text
  async generateSpeech(request: TavusSpeechRequest): Promise<TavusSpeechResponse> {
    // Add a default callback URL if not provided (required by API)
    const requestWithCallback = {
      ...request,
      callback_url: request.callback_url || 'https://httpbin.org/post'
    };

    return this.makeRequest<TavusSpeechResponse>('/speech', {
      method: 'POST',
      body: JSON.stringify(requestWithCallback),
    });
  }

  // Get speech status and details
  async getSpeech(speechId: string): Promise<TavusSpeechStatus> {
    return this.makeRequest<TavusSpeechStatus>(`/speech/${speechId}`);
  }

  // Get list of speeches
  async getSpeeches(params?: {
    limit?: number;
    page?: number;
  }): Promise<{ data: TavusSpeechStatus[]; total_count: number }> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.page) searchParams.append('page', params.page.toString());

    const queryString = searchParams.toString();
    const endpoint = `/speech${queryString ? `?${queryString}` : ''}`;

    return this.makeRequest<{ data: TavusSpeechStatus[]; total_count: number }>(endpoint);
  }

  // Delete a speech
  async deleteSpeech(speechId: string): Promise<void> {
    await this.makeRequest(`/speech/${speechId}`, {
      method: 'DELETE',
    });
  }
}

export const tavusApi = new TavusApiService();
