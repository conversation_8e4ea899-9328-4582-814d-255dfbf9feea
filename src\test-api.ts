// Test script to verify Tavus API connection
import { tavusApi } from './services/tavusApi';

async function testTavusAPI() {
  console.log('🧪 Testing Tavus API connection...');
  
  try {
    // Test 1: Get replicas
    console.log('📋 Fetching available replicas...');
    const replicas = await tavusApi.getReplicas({ verbose: true });
    console.log('✅ Replicas fetched successfully:', replicas);
    
    if (replicas.data.length === 0) {
      console.log('⚠️ No replicas found. Trying to get system replicas...');
      const systemReplicas = await tavusApi.getReplicas({ 
        replica_type: 'system',
        verbose: true 
      });
      console.log('📋 System replicas:', systemReplicas);
    }
    
    // Test 2: Generate speech (only if we have replicas)
    if (replicas.data.length > 0 || replicas.total_count > 0) {
      const firstReplica = replicas.data[0];
      console.log(`🎤 Testing speech generation with replica: ${firstReplica.replica_name}`);
      
      const speechResponse = await tavusApi.generateSpeech({
        script: 'Hello! This is a test of the Tavus text-to-speech API.',
        replica_id: firstReplica.replica_id,
        speech_name: 'API Test Speech'
      });
      
      console.log('✅ Speech generated successfully:', speechResponse);
      
      // Test 3: Get speech details
      console.log('📄 Fetching speech details...');
      const speechDetails = await tavusApi.getSpeech(speechResponse.speech_id);
      console.log('✅ Speech details:', speechDetails);
      
    } else {
      console.log('⚠️ No replicas available for speech generation test');
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error);
    
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      
      // Check for common issues
      if (error.message.includes('401')) {
        console.error('🔑 Authentication failed. Please check your API key.');
      } else if (error.message.includes('403')) {
        console.error('🚫 Access forbidden. Please check your API permissions.');
      } else if (error.message.includes('404')) {
        console.error('🔍 Resource not found. Please check the API endpoint.');
      } else if (error.message.includes('429')) {
        console.error('⏰ Rate limit exceeded. Please wait before trying again.');
      }
    }
  }
}

// Export for use in browser console
(window as any).testTavusAPI = testTavusAPI;

console.log('🚀 Tavus API test script loaded. Run testTavusAPI() in the console to test the API.');
