import React, { useState, useEffect, useRef } from 'react';
import { tavusApi } from '../services/tavusApi';
import type { TavusReplica, TavusSpeechResponse } from '../services/tavusApi';

interface TextToSpeechProps {
  className?: string;
}

const TextToSpeech: React.FC<TextToSpeechProps> = ({ className }) => {
  const [text, setText] = useState('Hello! Welcome to the Tavus text-to-speech demo. This is a sample text that you can use to test the AI voice generation.');
  const [replicas, setReplicas] = useState<TavusReplica[]>([]);
  const [selectedReplicaId, setSelectedReplicaId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingReplicas, setIsLoadingReplicas] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isAudioLoading, setIsAudioLoading] = useState(false);
  const [speechHistory, setSpeechHistory] = useState<TavusSpeechResponse[]>([]);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  // Load replicas on component mount
  useEffect(() => {
    loadReplicas();
  }, []);

  const loadReplicas = async () => {
    try {
      setIsLoadingReplicas(true);
      setError(null);
      
      // First try to get user replicas
      const userReplicas = await tavusApi.getReplicas({ 
        replica_type: 'user',
        verbose: true 
      });
      
      // Then get system/stock replicas as fallback
      const systemReplicas = await tavusApi.getReplicas({ 
        replica_type: 'system',
        verbose: true 
      });
      
      const allReplicas = [...userReplicas.data, ...systemReplicas.data];
      setReplicas(allReplicas);
      
      // Auto-select first available replica
      if (allReplicas.length > 0) {
        setSelectedReplicaId(allReplicas[0].replica_id);
      }
    } catch (err) {
      setError(`Failed to load replicas: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoadingReplicas(false);
    }
  };

  const generateSpeech = async () => {
    if (!text.trim()) {
      setError('Please enter some text to convert to speech');
      return;
    }

    if (!selectedReplicaId) {
      setError('Please select a voice replica');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setAudioUrl(null);

      console.log('🎤 Generating speech with:', {
        script: text.trim(),
        replica_id: selectedReplicaId
      });

      const response = await tavusApi.generateSpeech({
        script: text.trim(),
        replica_id: selectedReplicaId,
        speech_name: `Speech ${new Date().toLocaleString()}`,
        callback_url: 'https://httpbin.org/post' // Demo callback URL
      });

      console.log('✅ Speech generated successfully:', response);
      setAudioUrl(response.speech_file_url);
      setSpeechHistory(prev => [response, ...prev.slice(0, 4)]); // Keep last 5 speeches

    } catch (err) {
      console.error('❌ Speech generation failed:', err);
      let errorMessage = 'Unknown error occurred';

      if (err instanceof Error) {
        errorMessage = err.message;

        // Provide more user-friendly error messages
        if (err.message.includes('401')) {
          errorMessage = 'Authentication failed. Please check the API key.';
        } else if (err.message.includes('403')) {
          errorMessage = 'Access forbidden. Please check API permissions.';
        } else if (err.message.includes('429')) {
          errorMessage = 'Rate limit exceeded. Please wait a moment and try again.';
        } else if (err.message.includes('400')) {
          errorMessage = 'Invalid request. Please check your input and try again.';
        }
      }

      setError(`Failed to generate speech: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const playAudio = async () => {
    if (audioRef.current && audioUrl) {
      try {
        console.log('🎵 Attempting to play audio:', audioUrl);

        // Reset the audio to the beginning
        audioRef.current.currentTime = 0;

        // Attempt to play
        const playPromise = audioRef.current.play();

        if (playPromise !== undefined) {
          await playPromise;
          setIsPlaying(true);
          console.log('✅ Audio playback started successfully');
        }
      } catch (error) {
        console.error('❌ Audio playback failed:', error);
        setError(`Audio playback failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setIsPlaying(false);
      }
    } else {
      console.warn('⚠️ No audio reference or URL available');
      setError('No audio available to play');
    }
  };

  const pauseAudio = () => {
    if (audioRef.current) {
      try {
        audioRef.current.pause();
        setIsPlaying(false);
        console.log('⏸️ Audio paused');
      } catch (error) {
        console.error('❌ Audio pause failed:', error);
      }
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
    console.log('🔚 Audio playback ended');
  };

  const handleAudioError = (event: React.SyntheticEvent<HTMLAudioElement, Event>) => {
    const audio = event.currentTarget;
    console.error('❌ Audio error:', audio.error);
    setIsPlaying(false);

    let errorMessage = 'Audio playback error';
    if (audio.error) {
      switch (audio.error.code) {
        case MediaError.MEDIA_ERR_ABORTED:
          errorMessage = 'Audio playback was aborted';
          break;
        case MediaError.MEDIA_ERR_NETWORK:
          errorMessage = 'Network error occurred while loading audio';
          break;
        case MediaError.MEDIA_ERR_DECODE:
          errorMessage = 'Audio decoding error';
          break;
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMessage = 'Audio format not supported';
          break;
        default:
          errorMessage = 'Unknown audio error';
      }
    }

    setError(errorMessage);
  };

  const handleAudioCanPlay = () => {
    console.log('✅ Audio can play - ready for playback');
    setIsAudioLoading(false);
  };

  const handleAudioLoadStart = () => {
    console.log('🔄 Audio loading started');
    setIsAudioLoading(true);
  };

  const playHistoryAudio = async (url: string) => {
    console.log('🔄 Loading history audio:', url);
    setAudioUrl(url);
    setError(null);

    // Wait for the audio element to update with the new URL
    setTimeout(async () => {
      if (audioRef.current) {
        try {
          // Load the new audio
          audioRef.current.load();

          // Wait for it to be ready
          await new Promise((resolve, reject) => {
            const audio = audioRef.current!;
            const onCanPlay = () => {
              audio.removeEventListener('canplay', onCanPlay);
              audio.removeEventListener('error', onError);
              resolve(void 0);
            };
            const onError = () => {
              audio.removeEventListener('canplay', onCanPlay);
              audio.removeEventListener('error', onError);
              reject(new Error('Failed to load audio'));
            };

            audio.addEventListener('canplay', onCanPlay);
            audio.addEventListener('error', onError);
          });

          // Now play
          const playPromise = audioRef.current.play();
          if (playPromise !== undefined) {
            await playPromise;
            setIsPlaying(true);
            console.log('✅ History audio playback started');
          }
        } catch (error) {
          console.error('❌ History audio playback failed:', error);
          setError(`Failed to play audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
          setIsPlaying(false);
        }
      }
    }, 100);
  };

  const loadSampleText = (sampleText: string) => {
    setText(sampleText);
  };

  const sampleTexts = [
    "Hello! Welcome to the Tavus text-to-speech demo. This is a sample text that you can use to test the AI voice generation.",
    "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet.",
    "Artificial intelligence is transforming the way we interact with technology, making it more natural and intuitive.",
    "Good morning! I hope you're having a wonderful day. The weather looks beautiful today.",
    "Thank you for trying out our text-to-speech technology. We hope you find it useful and engaging."
  ];

  return (
    <div className={`text-to-speech-container ${className || ''}`}>
      <div className="header">
        <h1>🎤 Tavus Text-to-Speech Demo</h1>
        <p>Convert your text to natural-sounding speech using AI-powered voice replicas</p>
      </div>

      {error && (
        <div className="error-message">
          <strong>Error:</strong> {error}
          <button onClick={() => setError(null)} className="close-error">×</button>
        </div>
      )}

      <div className="main-content">
        <div className="input-section">
          <div className="voice-selector">
            <label htmlFor="replica-select">Choose Voice:</label>
            {isLoadingReplicas ? (
              <div className="loading">Loading voices...</div>
            ) : (
              <select
                id="replica-select"
                value={selectedReplicaId}
                onChange={(e) => setSelectedReplicaId(e.target.value)}
                disabled={replicas.length === 0}
              >
                {replicas.length === 0 ? (
                  <option value="">No voices available</option>
                ) : (
                  replicas.map((replica) => (
                    <option key={replica.replica_id} value={replica.replica_id}>
                      {replica.replica_name} ({replica.replica_type})
                    </option>
                  ))
                )}
              </select>
            )}
            <button onClick={loadReplicas} className="refresh-btn" disabled={isLoadingReplicas}>
              🔄 Refresh
            </button>
          </div>

          <div className="text-input">
            <label htmlFor="text-area">Enter Text:</label>
            <textarea
              id="text-area"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Type the text you want to convert to speech..."
              rows={4}
              maxLength={1000}
            />
            <div className="char-count">{text.length}/1000 characters</div>
          </div>

          <div className="sample-texts">
            <label>Quick samples:</label>
            <div className="sample-buttons">
              {sampleTexts.map((sample, index) => (
                <button
                  key={index}
                  onClick={() => loadSampleText(sample)}
                  className="sample-btn"
                  title={sample}
                >
                  Sample {index + 1}
                </button>
              ))}
            </div>
          </div>

          <button
            onClick={generateSpeech}
            disabled={isLoading || !text.trim() || !selectedReplicaId}
            className="generate-btn"
          >
            {isLoading ? '🔄 Generating...' : '🎵 Generate Speech'}
          </button>
        </div>

        {audioUrl && (
          <div className="audio-section">
            <h3>Generated Speech</h3>
            <div className="audio-controls">
              <button
                onClick={isPlaying ? pauseAudio : playAudio}
                className="play-pause-btn"
                disabled={isAudioLoading}
              >
                {isAudioLoading ? '🔄 Loading...' : isPlaying ? '⏸️ Pause' : '▶️ Play'}
              </button>
              <a href={audioUrl} download className="download-btn">
                💾 Download
              </a>
            </div>
            <audio
              ref={audioRef}
              src={audioUrl}
              onEnded={handleAudioEnded}
              onError={handleAudioError}
              onCanPlay={handleAudioCanPlay}
              onLoadStart={handleAudioLoadStart}
              preload="auto"
              crossOrigin="anonymous"
            />

            {/* Debug info */}
            <div className="audio-debug">
              <small>Audio URL: {audioUrl}</small>
              <br />
              <small>Status: {isAudioLoading ? 'Loading...' : isPlaying ? 'Playing' : 'Ready'}</small>
              <br />
              <button
                onClick={() => window.open(audioUrl || '', '_blank')}
                className="test-url-btn"
                disabled={!audioUrl}
              >
                🔗 Test URL
              </button>
            </div>
          </div>
        )}

        {speechHistory.length > 0 && (
          <div className="history-section">
            <h3>Recent Speeches</h3>
            <div className="history-list">
              {speechHistory.map((speech, index) => (
                <div key={speech.speech_id} className="history-item">
                  <span className="speech-name">{speech.speech_name}</span>
                  <button
                    onClick={() => playHistoryAudio(speech.speech_file_url)}
                    className="play-history-btn"
                  >
                    ▶️ Play
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TextToSpeech;
